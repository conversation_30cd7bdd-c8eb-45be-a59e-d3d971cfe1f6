[profile.default]
src = "src"
out = "out"
libs = ["lib"]
via_ir = true  # switch to the IR pipeline (equivalent to --via-ir)
optimizer = true
optimizer_runs = 200

# --- Hedera Testnet ---
[rpc_endpoints]
hedera_testnet = "https://testnet.hashio.io/api"

[profile.chain_ids]
hedera_testnet = 296

# optional: default to hedera testnet so you can run plain `forge script`
[profile.default.fork]
chain = "hedera_testnet"

# See more config options https://github.com/foundry-rs/foundry/blob/master/crates/config/README.md#all-options
