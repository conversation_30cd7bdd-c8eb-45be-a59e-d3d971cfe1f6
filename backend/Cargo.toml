[package]
name = "yieldera"
version = "0.1.0"
edition = "2024"

[dependencies]
actix-web = "4.11.0"
alloy = { version = "1.0.23", features = ["full"] }
color-eyre = "0.6.5"
dashmap = "6.1.0"
dotenvy = "0.15.7"
once_cell = "1.21.3"
serde = { version = "1.0.219", features = ["derive"] }
serde_json = "1.0.141"
thiserror = "2.0.12"
tokio = { version = "1.46.1", features = ["full"] }
toml = "0.9.3"
tracing = "0.1.41"
tracing-appender = "0.2.3"
tracing-subscriber = { version = "0.3.19", features = ["env-filter"] }
utoipa = { version = "5.4.0", features = ["actix_extras"] }
utoipa-actix-web = "0.1.2"
utoipa-swagger-ui = { version = "9.0.2", features = ["actix-web", "reqwest"] }
