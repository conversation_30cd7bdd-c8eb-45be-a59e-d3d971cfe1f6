[{"type": "constructor", "inputs": [{"name": "_pool", "type": "address", "internalType": "address"}], "stateMutability": "nonpayable"}, {"type": "fallback", "stateMutability": "payable"}, {"type": "receive", "stateMutability": "payable"}, {"type": "function", "name": "PRECISION", "inputs": [], "outputs": [{"name": "", "type": "uint256", "internalType": "uint256"}], "stateMutability": "view"}, {"type": "function", "name": "allowance", "inputs": [{"name": "owner", "type": "address", "internalType": "address"}, {"name": "spender", "type": "address", "internalType": "address"}], "outputs": [{"name": "", "type": "uint256", "internalType": "uint256"}], "stateMutability": "view"}, {"type": "function", "name": "approve", "inputs": [{"name": "spender", "type": "address", "internalType": "address"}, {"name": "value", "type": "uint256", "internalType": "uint256"}], "outputs": [{"name": "", "type": "bool", "internalType": "bool"}], "stateMutability": "nonpayable"}, {"type": "function", "name": "associateToken", "inputs": [{"name": "token", "type": "address", "internalType": "address"}], "outputs": [], "stateMutability": "nonpayable"}, {"type": "function", "name": "associateVaultTokens", "inputs": [], "outputs": [], "stateMutability": "nonpayable"}, {"type": "function", "name": "balanceOf", "inputs": [{"name": "account", "type": "address", "internalType": "address"}], "outputs": [{"name": "", "type": "uint256", "internalType": "uint256"}], "stateMutability": "view"}, {"type": "function", "name": "burnAllLiquidity", "inputs": [], "outputs": [{"name": "amount0", "type": "uint256", "internalType": "uint256"}, {"name": "amount1", "type": "uint256", "internalType": "uint256"}], "stateMutability": "nonpayable"}, {"type": "function", "name": "currentTick", "inputs": [], "outputs": [{"name": "tick", "type": "int24", "internalType": "int24"}], "stateMutability": "view"}, {"type": "function", "name": "decimals", "inputs": [], "outputs": [{"name": "", "type": "uint8", "internalType": "uint8"}], "stateMutability": "view"}, {"type": "function", "name": "deposit", "inputs": [{"name": "deposit0", "type": "uint256", "internalType": "uint256"}, {"name": "deposit1", "type": "uint256", "internalType": "uint256"}, {"name": "to", "type": "address", "internalType": "address"}], "outputs": [{"name": "shares", "type": "uint256", "internalType": "uint256"}], "stateMutability": "payable"}, {"type": "function", "name": "fee", "inputs": [], "outputs": [{"name": "", "type": "uint24", "internalType": "uint24"}], "stateMutability": "view"}, {"type": "function", "name": "getCurrentPosition", "inputs": [], "outputs": [{"name": "liquidity", "type": "uint128", "internalType": "uint128"}, {"name": "amount0", "type": "uint256", "internalType": "uint256"}, {"name": "amount1", "type": "uint256", "internalType": "uint256"}], "stateMutability": "view"}, {"type": "function", "name": "getTotalAmounts", "inputs": [], "outputs": [{"name": "total0", "type": "uint256", "internalType": "uint256"}, {"name": "total1", "type": "uint256", "internalType": "uint256"}], "stateMutability": "view"}, {"type": "function", "name": "isActive", "inputs": [], "outputs": [{"name": "", "type": "bool", "internalType": "bool"}], "stateMutability": "view"}, {"type": "function", "name": "isToken0Native", "inputs": [], "outputs": [{"name": "", "type": "bool", "internalType": "bool"}], "stateMutability": "view"}, {"type": "function", "name": "isToken1Native", "inputs": [], "outputs": [{"name": "", "type": "bool", "internalType": "bool"}], "stateMutability": "view"}, {"type": "function", "name": "isVaultTokensAssociated", "inputs": [], "outputs": [{"name": "", "type": "bool", "internalType": "bool"}], "stateMutability": "view"}, {"type": "function", "name": "lowerTick", "inputs": [], "outputs": [{"name": "", "type": "int24", "internalType": "int24"}], "stateMutability": "view"}, {"type": "function", "name": "mintLiquidity", "inputs": [{"name": "amount0Max", "type": "uint256", "internalType": "uint256"}, {"name": "amount1Max", "type": "uint256", "internalType": "uint256"}, {"name": "tickLower", "type": "int24", "internalType": "int24"}, {"name": "tickUpper", "type": "int24", "internalType": "int24"}], "outputs": [{"name": "liquidity", "type": "uint128", "internalType": "uint128"}, {"name": "amount0", "type": "uint256", "internalType": "uint256"}, {"name": "amount1", "type": "uint256", "internalType": "uint256"}], "stateMutability": "payable"}, {"type": "function", "name": "name", "inputs": [], "outputs": [{"name": "", "type": "string", "internalType": "string"}], "stateMutability": "view"}, {"type": "function", "name": "owner", "inputs": [], "outputs": [{"name": "", "type": "address", "internalType": "address"}], "stateMutability": "view"}, {"type": "function", "name": "performanceFee", "inputs": [], "outputs": [{"name": "", "type": "uint24", "internalType": "uint24"}], "stateMutability": "view"}, {"type": "function", "name": "pool", "inputs": [], "outputs": [{"name": "", "type": "address", "internalType": "address"}], "stateMutability": "view"}, {"type": "function", "name": "poolContract", "inputs": [], "outputs": [{"name": "", "type": "address", "internalType": "contract IUniswapV3Pool"}], "stateMutability": "view"}, {"type": "function", "name": "rebalance", "inputs": [{"name": "newLowerTick", "type": "int24", "internalType": "int24"}, {"name": "newUpperTick", "type": "int24", "internalType": "int24"}, {"name": "desiredSwapOutAmount", "type": "uint256", "internalType": "uint256"}, {"name": "amountInMax", "type": "uint256", "internalType": "uint256"}, {"name": "isSwap0To1", "type": "bool", "internalType": "bool"}], "outputs": [{"name": "amount0", "type": "uint256", "internalType": "uint256"}, {"name": "amount1", "type": "uint256", "internalType": "uint256"}, {"name": "liquidity", "type": "uint128", "internalType": "uint128"}], "stateMutability": "payable"}, {"type": "function", "name": "renounceOwnership", "inputs": [], "outputs": [], "stateMutability": "nonpayable"}, {"type": "function", "name": "setPerformanceFee", "inputs": [{"name": "_performanceFee", "type": "uint24", "internalType": "uint24"}], "outputs": [], "stateMutability": "nonpayable"}, {"type": "function", "name": "sweep", "inputs": [{"name": "token", "type": "address", "internalType": "address"}, {"name": "to", "type": "address", "internalType": "address"}], "outputs": [], "stateMutability": "nonpayable"}, {"type": "function", "name": "symbol", "inputs": [], "outputs": [{"name": "", "type": "string", "internalType": "string"}], "stateMutability": "view"}, {"type": "function", "name": "tickSpacing", "inputs": [], "outputs": [{"name": "", "type": "int24", "internalType": "int24"}], "stateMutability": "view"}, {"type": "function", "name": "token0", "inputs": [], "outputs": [{"name": "", "type": "address", "internalType": "address"}], "stateMutability": "view"}, {"type": "function", "name": "token1", "inputs": [], "outputs": [{"name": "", "type": "address", "internalType": "address"}], "stateMutability": "view"}, {"type": "function", "name": "totalSupply", "inputs": [], "outputs": [{"name": "", "type": "uint256", "internalType": "uint256"}], "stateMutability": "view"}, {"type": "function", "name": "transfer", "inputs": [{"name": "to", "type": "address", "internalType": "address"}, {"name": "value", "type": "uint256", "internalType": "uint256"}], "outputs": [{"name": "", "type": "bool", "internalType": "bool"}], "stateMutability": "nonpayable"}, {"type": "function", "name": "transferFrom", "inputs": [{"name": "from", "type": "address", "internalType": "address"}, {"name": "to", "type": "address", "internalType": "address"}, {"name": "value", "type": "uint256", "internalType": "uint256"}], "outputs": [{"name": "", "type": "bool", "internalType": "bool"}], "stateMutability": "nonpayable"}, {"type": "function", "name": "transferOwnership", "inputs": [{"name": "new<PERSON>wner", "type": "address", "internalType": "address"}], "outputs": [], "stateMutability": "nonpayable"}, {"type": "function", "name": "uniswapV3MintCallback", "inputs": [{"name": "amount0", "type": "uint256", "internalType": "uint256"}, {"name": "amount1", "type": "uint256", "internalType": "uint256"}, {"name": "data", "type": "bytes", "internalType": "bytes"}], "outputs": [], "stateMutability": "nonpayable"}, {"type": "function", "name": "unwrap<PERSON><PERSON>bar", "inputs": [{"name": "amount", "type": "uint256", "internalType": "uint256"}], "outputs": [], "stateMutability": "nonpayable"}, {"type": "function", "name": "upperTick", "inputs": [], "outputs": [{"name": "", "type": "int24", "internalType": "int24"}], "stateMutability": "view"}, {"type": "function", "name": "vaultFees0", "inputs": [], "outputs": [{"name": "", "type": "uint256", "internalType": "uint256"}], "stateMutability": "view"}, {"type": "function", "name": "vaultFees1", "inputs": [], "outputs": [{"name": "", "type": "uint256", "internalType": "uint256"}], "stateMutability": "view"}, {"type": "function", "name": "withdraw", "inputs": [{"name": "shares", "type": "uint256", "internalType": "uint256"}, {"name": "to", "type": "address", "internalType": "address"}], "outputs": [{"name": "amount0", "type": "uint256", "internalType": "uint256"}, {"name": "amount1", "type": "uint256", "internalType": "uint256"}], "stateMutability": "nonpayable"}, {"type": "function", "name": "withdrawNative", "inputs": [{"name": "amount", "type": "uint256", "internalType": "uint256"}, {"name": "to", "type": "address", "internalType": "address"}], "outputs": [], "stateMutability": "nonpayable"}, {"type": "function", "name": "wrapHbar", "inputs": [{"name": "amount", "type": "uint256", "internalType": "uint256"}], "outputs": [], "stateMutability": "payable"}, {"type": "event", "name": "Approval", "inputs": [{"name": "owner", "type": "address", "indexed": true, "internalType": "address"}, {"name": "spender", "type": "address", "indexed": true, "internalType": "address"}, {"name": "value", "type": "uint256", "indexed": false, "internalType": "uint256"}], "anonymous": false}, {"type": "event", "name": "Approval", "inputs": [{"name": "from", "type": "address", "indexed": true, "internalType": "address"}, {"name": "to", "type": "address", "indexed": true, "internalType": "address"}, {"name": "value", "type": "uint256", "indexed": false, "internalType": "uint256"}, {"name": "token", "type": "address", "indexed": true, "internalType": "address"}], "anonymous": false}, {"type": "event", "name": "AssociateToken", "inputs": [{"name": "token", "type": "address", "indexed": true, "internalType": "address"}], "anonymous": false}, {"type": "event", "name": "BurnAllLiquidity", "inputs": [{"name": "sender", "type": "address", "indexed": true, "internalType": "address"}, {"name": "amount0", "type": "uint256", "indexed": false, "internalType": "uint256"}, {"name": "amount1", "type": "uint256", "indexed": false, "internalType": "uint256"}], "anonymous": false}, {"type": "event", "name": "CollectFees", "inputs": [{"name": "sender", "type": "address", "indexed": true, "internalType": "address"}, {"name": "fees0", "type": "uint256", "indexed": false, "internalType": "uint256"}, {"name": "fees1", "type": "uint256", "indexed": false, "internalType": "uint256"}], "anonymous": false}, {"type": "event", "name": "CustomEvent", "inputs": [{"name": "sender", "type": "address", "indexed": true, "internalType": "address"}, {"name": "message", "type": "string", "indexed": false, "internalType": "string"}], "anonymous": false}, {"type": "event", "name": "DecreaseLiquidity", "inputs": [{"name": "sender", "type": "address", "indexed": true, "internalType": "address"}, {"name": "tokenId", "type": "uint256", "indexed": false, "internalType": "uint256"}, {"name": "liquidity", "type": "uint128", "indexed": false, "internalType": "uint128"}, {"name": "amount0", "type": "uint256", "indexed": false, "internalType": "uint256"}, {"name": "amount1", "type": "uint256", "indexed": false, "internalType": "uint256"}], "anonymous": false}, {"type": "event", "name": "<PERSON><PERSON><PERSON><PERSON>", "inputs": [{"name": "sender", "type": "address", "indexed": true, "internalType": "address"}, {"name": "to", "type": "address", "indexed": true, "internalType": "address"}, {"name": "shares", "type": "uint256", "indexed": false, "internalType": "uint256"}, {"name": "amount0", "type": "uint256", "indexed": false, "internalType": "uint256"}, {"name": "amount1", "type": "uint256", "indexed": false, "internalType": "uint256"}], "anonymous": false}, {"type": "event", "name": "IncreaseLiquidity", "inputs": [{"name": "sender", "type": "address", "indexed": true, "internalType": "address"}, {"name": "tokenId", "type": "uint256", "indexed": false, "internalType": "uint256"}, {"name": "liquidity", "type": "uint128", "indexed": false, "internalType": "uint128"}, {"name": "amount0", "type": "uint256", "indexed": false, "internalType": "uint256"}, {"name": "amount1", "type": "uint256", "indexed": false, "internalType": "uint256"}], "anonymous": false}, {"type": "event", "name": "MintLiquidity", "inputs": [{"name": "sender", "type": "address", "indexed": true, "internalType": "address"}, {"name": "tokenId", "type": "uint256", "indexed": false, "internalType": "uint256"}, {"name": "liquidity", "type": "uint128", "indexed": false, "internalType": "uint128"}, {"name": "amount0", "type": "uint256", "indexed": false, "internalType": "uint256"}, {"name": "amount1", "type": "uint256", "indexed": false, "internalType": "uint256"}], "anonymous": false}, {"type": "event", "name": "OwnershipTransferred", "inputs": [{"name": "previousOwner", "type": "address", "indexed": true, "internalType": "address"}, {"name": "new<PERSON>wner", "type": "address", "indexed": true, "internalType": "address"}], "anonymous": false}, {"type": "event", "name": "Rebalance", "inputs": [{"name": "tick", "type": "int24", "indexed": false, "internalType": "int24"}, {"name": "totalAmount0", "type": "uint256", "indexed": false, "internalType": "uint256"}, {"name": "totalAmount1", "type": "uint256", "indexed": false, "internalType": "uint256"}, {"name": "feeAmount0", "type": "uint256", "indexed": false, "internalType": "uint256"}, {"name": "feeAmount1", "type": "uint256", "indexed": false, "internalType": "uint256"}, {"name": "totalSupply", "type": "uint256", "indexed": false, "internalType": "uint256"}], "anonymous": false}, {"type": "event", "name": "Transfer", "inputs": [{"name": "from", "type": "address", "indexed": true, "internalType": "address"}, {"name": "to", "type": "address", "indexed": true, "internalType": "address"}, {"name": "value", "type": "uint256", "indexed": false, "internalType": "uint256"}], "anonymous": false}, {"type": "event", "name": "Transfer", "inputs": [{"name": "from", "type": "address", "indexed": true, "internalType": "address"}, {"name": "to", "type": "address", "indexed": true, "internalType": "address"}, {"name": "value", "type": "uint256", "indexed": false, "internalType": "uint256"}, {"name": "token", "type": "address", "indexed": true, "internalType": "address"}], "anonymous": false}, {"type": "event", "name": "Withdraw", "inputs": [{"name": "sender", "type": "address", "indexed": true, "internalType": "address"}, {"name": "to", "type": "address", "indexed": true, "internalType": "address"}, {"name": "shares", "type": "uint256", "indexed": false, "internalType": "uint256"}, {"name": "amount0", "type": "uint256", "indexed": false, "internalType": "uint256"}, {"name": "amount1", "type": "uint256", "indexed": false, "internalType": "uint256"}], "anonymous": false}, {"type": "error", "name": "AssociateFail", "inputs": [{"name": "respCode", "type": "int256", "internalType": "int256"}]}, {"type": "error", "name": "ERC20InsufficientAllowance", "inputs": [{"name": "spender", "type": "address", "internalType": "address"}, {"name": "allowance", "type": "uint256", "internalType": "uint256"}, {"name": "needed", "type": "uint256", "internalType": "uint256"}]}, {"type": "error", "name": "ERC20InsufficientBalance", "inputs": [{"name": "sender", "type": "address", "internalType": "address"}, {"name": "balance", "type": "uint256", "internalType": "uint256"}, {"name": "needed", "type": "uint256", "internalType": "uint256"}]}, {"type": "error", "name": "ERC20InvalidApprover", "inputs": [{"name": "approver", "type": "address", "internalType": "address"}]}, {"type": "error", "name": "ERC20InvalidReceiver", "inputs": [{"name": "receiver", "type": "address", "internalType": "address"}]}, {"type": "error", "name": "ERC20InvalidSender", "inputs": [{"name": "sender", "type": "address", "internalType": "address"}]}, {"type": "error", "name": "ERC20InvalidSpender", "inputs": [{"name": "spender", "type": "address", "internalType": "address"}]}, {"type": "error", "name": "OwnableInvalidOwner", "inputs": [{"name": "owner", "type": "address", "internalType": "address"}]}, {"type": "error", "name": "OwnableUnauthorizedAccount", "inputs": [{"name": "account", "type": "address", "internalType": "address"}]}, {"type": "error", "name": "ReentrancyGuardReentrantCall", "inputs": []}, {"type": "error", "name": "RespCode", "inputs": [{"name": "respCode", "type": "int32", "internalType": "int32"}]}, {"type": "error", "name": "SafeCastOverflowedIntDowncast", "inputs": [{"name": "bits", "type": "uint8", "internalType": "uint8"}, {"name": "value", "type": "int256", "internalType": "int256"}]}, {"type": "error", "name": "SafeERC20FailedOperation", "inputs": [{"name": "token", "type": "address", "internalType": "address"}]}]